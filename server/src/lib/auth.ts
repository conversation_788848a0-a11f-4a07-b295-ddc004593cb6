import { betterAuth } from "better-auth";
import { admin, organization } from "better-auth/plugins";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

/**
 * Better Auth configuration with admin plugin
 */
export const auth: any = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  secret: process.env.BETTER_AUTH_SECRET!,
  baseURL: process.env.BETTER_AUTH_URL!,
  trustedOrigins: [
    "http://localhost:5173", // Client dev server
    "http://localhost:3000", // Server
    "https://safety-induction.netlify.app", // Production
  ],
  
  emailAndPassword: {
    enabled: true,
  },
  
  user: {
    additionalFields: {
      role: {
        type: "string",
        defaultValue: "user",
      },
    },
  },
  
  plugins: [
    admin({
      defaultRole: "user",
      adminRoles: ["admin", "superadmin"],
      adminUserIds: ["zJwIasqudRzFQLbpODgEDwdNQTY9CGgT"], // Super admin user
    }),
    organization({
      allowUserToCreateOrganization: true,
      organizationCreation: {
        disabled: false,
        beforeCreate: async ({ organization, user }, request) => {
          // Prevent superadmin from becoming a member of the organization
          const userWithRole = user as any; // Cast to access additional fields
          if (userWithRole.role === 'superadmin') {
            // For superadmin, we'll handle membership separately
            // This hook can't prevent membership creation, so we'll use a different approach
            console.log('Superadmin creating organization:', organization.name);
          }
          return {
            data: organization
          }
        },
      },
      creatorRole: 'admin', // Set default role for non-superadmin creators
    }),
  ],
  
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  
  // Configure redirects based on user role
  redirects: {
    signIn: (ctx: any) => {
      // Check if request is from admin/superadmin path
      const referer = ctx.request.headers.get('referer') || '';
      if (referer.includes('/admin') || referer.includes('/superadmin')) {
        return '/login';
      }
      return '/user/login';
    },
    signOut: (ctx: any) => {
      // Check if request is from admin/superadmin path
      const referer = ctx.request.headers.get('referer') || '';
      if (referer.includes('/admin') || referer.includes('/superadmin')) {
        return '/login';
      }
      return '/user/login';
    },
    afterSignIn: (ctx: any) => {
      const user = ctx.user;
      if (user?.role === 'superadmin') {
        return '/superadmin/dashboard';
      } else if (user?.role === 'admin') {
        return '/admin/dashboard';
      }
      return '/user/quiz';
    },
    afterSignOut: (ctx: any) => {
      // Check if request is from admin/superadmin path
      const referer = ctx.request.headers.get('referer') || '';
      if (referer.includes('/admin') || referer.includes('/superadmin')) {
        return '/login';
      }
      return '/user/login';
    },
  },
});

export type Session = typeof auth.$Infer.Session;