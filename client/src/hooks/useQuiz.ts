import { useState, useEffect } from 'react';
import { useSession } from '../lib/auth-client';
import { Question } from '../types/quiz.types';

interface Quiz {
  id: string;
  title: string;
  description?: string;
  questions: Question[];
}

interface Video {
  id: string;
  title: string;
  youtubeUrl: string;
}

interface UserWithAdditionalFields {
  id: string;
  email: string;
  name: string;
  role?: string;
  organizationId?: string;
  tipePengunjung?: 'UMUM' | 'KARYAWAN';
}

export function useQuiz() {
  const { data: session, isPending } = useSession();
  const [user, setUser] = useState<UserWithAdditionalFields | null>(null);
  const [currentStep, setCurrentStep] = useState<'loading' | 'video' | 'quiz' | 'result'>('loading');
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [score, setScore] = useState(0);
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [video, setVideo] = useState<Video | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user profile when session is available
  useEffect(() => {
    const fetchUserProfile = async () => {
      // Wait for session to be loaded
      if (isPending) {
        return;
      }

      if (!session?.user) {
        setError('User tidak terautentikasi');
        setLoading(false);
        return;
      }

      try {
        const response = await fetch('/api/user/profile', {
          credentials: 'include'
        });
        const data = await response.json();

        if (data.success) {
          setUser(data.data);
        } else {
          setError('Gagal mengambil data user');
          setLoading(false);
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        setError('Terjadi kesalahan saat mengambil data user');
        setLoading(false);
      }
    };

    if (session?.user) {
      fetchUserProfile();
    }
  }, [session, isPending]);

  // Fetch quiz and video data based on user's organization and visitor type
  useEffect(() => {
    const fetchQuizData = async () => {
      if (!user?.organizationId || !user?.tipePengunjung) {
        if (user) {
          setError('Data user tidak lengkap - organisasi atau tipe pengunjung tidak ditemukan');
        }
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch quizzes for user's organization and visitor type
        const quizResponse = await fetch(
          `/api/quiz/organizations/${user.organizationId}/quizzes?visitorType=${user.tipePengunjung}`,
          { credentials: 'include' }
        );
        const quizData = await quizResponse.json();

        // Fetch videos for user's organization and visitor type
        const videoResponse = await fetch(
          `/api/video/organizations/${user.organizationId}/videos?visitorType=${user.tipePengunjung}`,
          { credentials: 'include' }
        );
        const videoData = await videoResponse.json();

        if (quizData.success && quizData.data.length > 0) {
          setQuiz(quizData.data[0]); // Take the first quiz
        } else {
          setError('Tidak ada quiz yang tersedia untuk tipe pengunjung Anda');
        }

        if (videoData.success && videoData.data.length > 0) {
          setVideo(videoData.data[0]); // Take the first video
        } else {
          setError('Tidak ada video yang tersedia untuk tipe pengunjung Anda');
        }

        setCurrentStep('video');
      } catch (error) {
        console.error('Error fetching quiz data:', error);
        setError('Gagal mengambil data quiz');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchQuizData();
    }
  }, [user]);

  const handleVideoComplete = () => {
    if (quiz && quiz.questions.length > 0) {
      setCurrentStep('quiz');
    } else {
      setError('Tidak ada pertanyaan quiz yang tersedia');
    }
  };

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
  };

  const handleNextQuestion = async () => {
    if (selectedAnswer === null || !quiz) return;

    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);

    let newScore = score;
    if (selectedAnswer === quiz.questions[currentQuestion].correctAnswer) {
      newScore = score + 1;
      setScore(newScore);
    }

    if (currentQuestion < quiz.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
    } else {
      // Quiz completed - calculate final score and submit
      const finalScore = Math.round(
        ((newScore + (selectedAnswer === quiz.questions[currentQuestion].correctAnswer ? 1 : 0)) / quiz.questions.length) * 100
      );

      try {
        // Submit quiz result to server
        const response = await fetch(`/api/quiz/${quiz.id}/submit`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            answers: [...newAnswers, selectedAnswer]
          }),
        });

        const result = await response.json();
        if (result.success) {
          setScore(result.data.score);
        } else {
          setScore(finalScore);
        }
      } catch (error) {
        console.error('Error submitting quiz:', error);
        setScore(finalScore);
      }

      setCurrentStep('result');
    }
  };

  const handleRetakeQuiz = () => {
    setCurrentStep('video');
    setCurrentQuestion(0);
    setAnswers([]);
    setSelectedAnswer(null);
    setScore(0);
  };

  return {
    currentStep,
    currentQuestion,
    answers,
    selectedAnswer,
    score,
    questions: quiz?.questions || [],
    quiz,
    video,
    loading,
    error,
    handleVideoComplete,
    handleAnswerSelect,
    handleNextQuestion,
    handleRetakeQuiz,
  };
}