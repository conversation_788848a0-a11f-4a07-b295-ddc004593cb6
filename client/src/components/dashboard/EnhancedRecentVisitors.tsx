import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import { Filter, Check, X } from 'lucide-react';

type VisitorData = {
  id: string;
  name: string;
  email: string;
  nomorTelepon?: string;
  tipePengunjung: 'UMUM' | 'KARYAWAN' | null;
  createdAt: string;
  status: 'approved' | 'pending' | 'rejected';
};

interface EnhancedRecentVisitorsProps {
  visitors: VisitorData[];
  onApprove?: (visitorId: string) => Promise<void>;
  onReject?: (visitorId: string) => Promise<void>;
}

export function EnhancedRecentVisitors({ visitors, onApprove, onReject }: EnhancedRecentVisitorsProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedDate, setSelectedDate] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [loadingActions, setLoadingActions] = useState<Record<string, boolean>>({});

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Approved</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Need Approval</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Disapproved</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getVisitorTypeBadge = (type: 'UMUM' | 'KARYAWAN' | null) => {
    if (type === 'KARYAWAN') {
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Karyawan</Badge>;
    }
    return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Umum</Badge>;
  };

  const filteredVisitors = visitors.filter(visitor => {
    const matchesSearch = visitor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         visitor.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' ||
                       (selectedType === 'karyawan' && visitor.tipePengunjung === 'KARYAWAN') ||
                       (selectedType === 'umum' && visitor.tipePengunjung === 'UMUM');
    const matchesDate = !selectedDate ||
                       new Date(visitor.createdAt).toDateString() === new Date(selectedDate).toDateString();

    return matchesSearch && matchesType && matchesDate;
  });

  const totalPages = Math.ceil(filteredVisitors.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedVisitors = filteredVisitors.slice(startIndex, startIndex + itemsPerPage);

  const handleApprove = async (visitorId: string) => {
    if (!onApprove) return;

    setLoadingActions(prev => ({ ...prev, [visitorId]: true }));
    try {
      await onApprove(visitorId);
    } catch (error) {
      console.error('Error approving visitor:', error);
    } finally {
      setLoadingActions(prev => ({ ...prev, [visitorId]: false }));
    }
  };

  const handleReject = async (visitorId: string) => {
    if (!onReject) return;

    if (!confirm('Are you sure you want to reject this visitor? This action cannot be undone and the visitor will be removed from the database.')) {
      return;
    }

    setLoadingActions(prev => ({ ...prev, [visitorId]: true }));
    try {
      await onReject(visitorId);
    } catch (error) {
      console.error('Error rejecting visitor:', error);
    } finally {
      setLoadingActions(prev => ({ ...prev, [visitorId]: false }));
    }
  };

  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900">
            Pengunjung Baru-baru Ini
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Tipe Pengunjung" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua</SelectItem>
                <SelectItem value="karyawan">Karyawan</SelectItem>
                <SelectItem value="umum">Umum</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-40"
            />
          </div>
        </div>

        {/* Search and Items per page */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-2">
            <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-gray-600">entries per page</span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Cari:</span>
            <Input
              placeholder="Search visitors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-600">No</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Nama</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Email</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Nomor Telepon (WA)</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Tipe Pengunjung</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Timestamp</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Visiting Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedVisitors.map((visitor, index) => (
                <tr key={visitor.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 text-gray-900">{startIndex + index + 1}</td>
                  <td className="py-3 px-4 text-gray-900">{visitor.name}</td>
                  <td className="py-3 px-4 text-gray-600">{visitor.email}</td>
                  <td className="py-3 px-4 text-gray-600">{visitor.nomorTelepon || '-'}</td>
                  <td className="py-3 px-4">{getVisitorTypeBadge(visitor.tipePengunjung)}</td>
                  <td className="py-3 px-4 text-gray-600">
                    {new Date(visitor.createdAt).toLocaleString('id-ID', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit'
                    })}
                  </td>
                  <td className="py-3 px-4">{getStatusBadge(visitor.status)}</td>
                  <td className="py-3 px-4">
                    {visitor.status === 'pending' && (
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-green-600 border-green-600 hover:bg-green-50"
                          onClick={() => handleApprove(visitor.id)}
                          disabled={loadingActions[visitor.id]}
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 border-red-600 hover:bg-red-50"
                          onClick={() => handleReject(visitor.id)}
                          disabled={loadingActions[visitor.id]}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                    {visitor.status === 'approved' && (
                      <span className="text-sm text-green-600">Approved</span>
                    )}
                    {visitor.status === 'rejected' && (
                      <span className="text-sm text-red-600">Rejected</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-600">
            Menampilkan {startIndex + 1} sampai {Math.min(startIndex + itemsPerPage, filteredVisitors.length)} dari {filteredVisitors.length} entri
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className={currentPage === pageNum ? "bg-blue-600 text-white" : ""}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
