import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';

type ChartDataPoint = {
  day: string;
  value: number;
};

interface VisitorChartProps {
  data: ChartDataPoint[];
  weeklyData?: ChartDataPoint[];
}

export function VisitorChart({ data, weeklyData }: VisitorChartProps) {
  const [viewType, setViewType] = useState<'harian' | 'mingguan'>('harian');

  // Use provided weekly data or fallback to sample data
  const defaultWeeklyData: ChartDataPoint[] = [
    { day: "Minggu 1", value: 280 },
    { day: "Minggu 2", value: 320 },
    { day: "Minggu 3", value: 290 },
    { day: "Minggu 4", value: 410 },
  ];

  const actualWeeklyData = weeklyData || defaultWeeklyData;
  const currentData = viewType === 'harian' ? data : actualWeeklyData;
  const timeLabel = viewType === 'harian' ? 'Dalam 7 hari terakhir' : 'Dalam 4 minggu terakhir';

  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">
          Total Pengunjung
        </CardTitle>
        <p className="text-sm text-gray-600">{timeLabel}</p>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            {viewType === 'harian' ? (
              <LineChart data={currentData}>
                <XAxis
                  dataKey="day"
                  axisLine={false}
                  tickLine={false}
                  className="text-xs text-gray-600"
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  className="text-xs text-gray-600"
                />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, fill: '#3b82f6' }}
                />
              </LineChart>
            ) : (
              <BarChart data={currentData}>
                <XAxis
                  dataKey="day"
                  axisLine={false}
                  tickLine={false}
                  className="text-xs text-gray-600"
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  className="text-xs text-gray-600"
                />
                <Bar
                  dataKey="value"
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>

        <div className="flex justify-center space-x-2 mt-4">
          <Button
            size="sm"
            onClick={() => setViewType('harian')}
            className={
              viewType === 'harian'
                ? "bg-orange-500 hover:bg-orange-600 text-white"
                : "text-gray-600 border-gray-300"
            }
            variant={viewType === 'harian' ? 'default' : 'outline'}
          >
            Harian
          </Button>
          <Button
            size="sm"
            onClick={() => setViewType('mingguan')}
            className={
              viewType === 'mingguan'
                ? "bg-orange-500 hover:bg-orange-600 text-white"
                : "text-gray-600 border-gray-300"
            }
            variant={viewType === 'mingguan' ? 'default' : 'outline'}
          >
            Mingguan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
