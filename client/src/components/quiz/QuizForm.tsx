import { useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Card, CardContent } from '../ui/card';
import { Plus, Trash2, X } from 'lucide-react';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  order: number;
}

interface Quiz {
  id?: string;
  title: string;
  description?: string;
  organizationId: string;
  visitorType: 'UMUM' | 'KARYAWAN';
  questions: Question[];
}

interface QuizFormProps {
  quiz?: Quiz | null;
  organizationId: string;
  visitorType: 'UMUM' | 'KARYAWAN';
  onSave: (quiz: Omit<Quiz, 'id'>) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export function QuizForm({
  quiz,
  organizationId,
  visitorType,
  onSave,
  onCancel,
  loading = false
}: QuizFormProps) {
  const [formData, setFormData] = useState<Omit<Quiz, 'id'>>({
    title: quiz?.title || '',
    description: quiz?.description || '',
    organizationId,
    visitorType,
    questions: quiz?.questions || [
      {
        id: 'q1',
        question: '',
        options: ['', '', '', ''],
        correctAnswer: 0,
        order: 0
      }
    ]
  });

  const addQuestion = () => {
    const newQuestion: Question = {
      id: `q${formData.questions.length + 1}`,
      question: '',
      options: ['', '', '', ''],
      correctAnswer: 0,
      order: formData.questions.length
    };
    setFormData({
      ...formData,
      questions: [...formData.questions, newQuestion]
    });
  };

  const removeQuestion = (index: number) => {
    if (formData.questions.length <= 1) return;
    const newQuestions = formData.questions.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      questions: newQuestions.map((q, i) => ({ ...q, order: i }))
    });
  };

  const updateQuestion = (index: number, field: keyof Question, value: any) => {
    const newQuestions = [...formData.questions];
    newQuestions[index] = { ...newQuestions[index], [field]: value };
    setFormData({ ...formData, questions: newQuestions });
  };

  const updateOption = (questionIndex: number, optionIndex: number, value: string) => {
    const newQuestions = [...formData.questions];
    newQuestions[questionIndex].options[optionIndex] = value;
    setFormData({ ...formData, questions: newQuestions });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.title.trim()) {
      alert('Judul quiz harus diisi');
      return;
    }

    for (let i = 0; i < formData.questions.length; i++) {
      const question = formData.questions[i];
      if (!question.question.trim()) {
        alert(`Pertanyaan ${i + 1} harus diisi`);
        return;
      }

      const filledOptions = question.options.filter(opt => opt.trim());
      if (filledOptions.length < 2) {
        alert(`Pertanyaan ${i + 1} harus memiliki minimal 2 pilihan jawaban`);
        return;
      }
    }

    await onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-900">
              {quiz ? 'Edit Quiz' : 'Tambah Quiz Baru'}
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Judul Quiz *
                </label>
                <Input
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Masukkan judul quiz"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipe Pengunjung
                </label>
                <Input
                  value={visitorType === 'KARYAWAN' ? 'Karyawan' : 'Umum'}
                  disabled
                  className="bg-gray-100"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deskripsi
              </label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Masukkan deskripsi quiz (opsional)"
                rows={3}
              />
            </div>

            {/* Questions */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Pertanyaan</h3>
                <Button
                  type="button"
                  onClick={addQuestion}
                  variant="outline"
                  size="sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Pertanyaan
                </Button>
              </div>

              <div className="space-y-6">
                {formData.questions.map((question, questionIndex) => (
                  <Card key={question.id} className="border border-gray-200">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="font-medium text-gray-900">
                          Pertanyaan {questionIndex + 1}
                        </h4>
                        {formData.questions.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeQuestion(questionIndex)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Pertanyaan *
                          </label>
                          <Textarea
                            value={question.question}
                            onChange={(e) => updateQuestion(questionIndex, 'question', e.target.value)}
                            placeholder="Masukkan pertanyaan"
                            rows={2}
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Pilihan Jawaban *
                          </label>
                          <div className="space-y-2">
                            {question.options.map((option, optionIndex) => (
                              <div key={optionIndex} className="flex items-center space-x-3">
                                <input
                                  type="radio"
                                  name={`correct-${question.id}`}
                                  checked={question.correctAnswer === optionIndex}
                                  onChange={() => updateQuestion(questionIndex, 'correctAnswer', optionIndex)}
                                  className="text-orange-500"
                                />
                                <span className="text-sm text-gray-500 w-6">
                                  {String.fromCharCode(97 + optionIndex)}.
                                </span>
                                <Input
                                  value={option}
                                  onChange={(e) => updateOption(questionIndex, optionIndex, e.target.value)}
                                  placeholder={`Pilihan ${String.fromCharCode(97 + optionIndex)}`}
                                  className="flex-1"
                                />
                              </div>
                            ))}
                          </div>
                          <p className="text-xs text-gray-500 mt-2">
                            Pilih radio button untuk menandai jawaban yang benar
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Atur Waktu Quiz */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Atur Waktu Quiz</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Atur Waktu Quiz</h4>
                  <div className="bg-gray-100 p-4 rounded-md border">
                    <span className="text-gray-500">Waktu dalam detik</span>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Atur Waktu Quiz</h4>
                  <div className="bg-gray-100 p-4 rounded-md border">
                    <span className="text-gray-500">Waktu dalam detik</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Konfigurasi Notifikasi */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Konfigurasi Notifikasi</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Nomor WhatsApp Perusahaan</h4>
                  <div className="bg-gray-100 p-4 rounded-md border h-[120px] flex items-center">
                    <span className="text-gray-500">+1 6357 63772</span>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Custom Message Template</h4>
                  <div className="relative">
                    <div className="bg-gray-100 p-4 rounded-md border h-[120px] flex items-start">
                      <span className="text-gray-500">Masukkan template pesan.</span>
                    </div>
                    <Button
                      type="button"
                      size="sm"
                      className="absolute bottom-2 right-2 bg-orange-500 hover:bg-orange-600 text-white h-8 w-8 p-0 rounded-md"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Konfigurasi Lama Waktu Kunjungan */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Konfigurasi Lama Waktu Kunjungan</h3>
              <div className="w-full mb-6">
                <div className="bg-gray-100 p-4 rounded-md border h-[60px] flex items-center">
                  <span className="text-gray-500">7 Hari</span>
                </div>
              </div>

              {/* Test Connection */}
              <div className="bg-gray-50 p-4 rounded-md border">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Tes koneksi dengan detail yang diberikan.</span>
                  <Button
                    type="button"
                    className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2"
                  >
                    Tes koneksi
                  </Button>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Batal
              </Button>
              <Button
                type="submit"
                className="bg-orange-500 hover:bg-orange-600 text-white"
                disabled={loading}
              >
                {loading ? 'Menyimpan...' : (quiz ? 'Update Quiz' : 'Simpan Quiz')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
