import { Navigate } from 'react-router-dom';
import { useSession } from '../../lib/auth-client';
import { VisitorSafetyQuiz } from '../../pages/user/VisitorSafetyQuiz';

/**
 * Component to handle visitor quiz route with proper role-based redirects
 * Ensures that authenticated superadmin and admin users are redirected to their dashboards
 * while redirecting unauthenticated users to login and allowing regular users to access the quiz
 */
export function VisitorQuizRoute() {
  const { data: session, isPending } = useSession();

  // Show loading while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // If user is not authenticated, redirect to login
  if (!session?.user) {
    return <Navigate to="/user/login" replace />;
  }

  // If user is authenticated, redirect based on role
  const userRole = session.user.role;

  if (userRole === 'superadmin') {
    return <Navigate to="/superadmin/dashboard" replace />;
  }

  if (userRole === 'admin') {
    return <Navigate to="/admin/dashboard" replace />;
  }

  // Allow regular users to access the quiz
  return <VisitorSafetyQuiz />;
}