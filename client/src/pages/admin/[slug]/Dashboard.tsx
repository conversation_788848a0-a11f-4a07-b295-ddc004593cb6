import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { StatsCards } from '../../../components/dashboard/StatsCards';
import { VisitorChart } from '../../../components/dashboard/VisitorChart';
import { HeatmapCard } from '../../../components/dashboard/HeatmapCard';
import { EnhancedRecentVisitors } from '../../../components/dashboard/EnhancedRecentVisitors';

// Types
export type DashboardStats = {
  totalPengunjungTerdaftar: number;
  totalPengunjungDisetujui: number;
  totalPengunjungDitolak: number;
  totalPengunjungKadaluwarsa: number;
};

export type ChartDataPoint = {
  day: string;
  value: number;
};

type WeeklyChartDataPoint = {
  day: string;
  value: number;
};

interface Organization {
  id: string;
  name: string;
  slug: string;
}

export type VisitorData = {
  id: string;
  name: string;
  email: string;
  nomorTelepon?: string;
  tipePengunjung: 'UMUM' | 'KARYAWAN' | null;
  organization: Organization;
  organizationId: string;
  createdAt: string;
  status: 'approved' | 'pending' | 'rejected';
};

// Using relative paths for single origin deployment

export function Dashboard() {
  const { slug } = useParams<{ slug: string }>();
  const [stats, setStats] = useState<DashboardStats>({
    totalPengunjungTerdaftar: 0,
    totalPengunjungDisetujui: 0,
    totalPengunjungDitolak: 0,
    totalPengunjungKadaluwarsa: 0,
  });
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [weeklyChartData, setWeeklyChartData] = useState<WeeklyChartDataPoint[]>([]);
  const [visitors, setVisitors] = useState<VisitorData[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Get organization info first
        if (slug) {
          const orgResponse = await fetch(`/api/organization/${slug}`, {
            credentials: 'include'
          });
          if (orgResponse.ok) {
            const orgResult = await orgResponse.json();
            if (orgResult.success) {
              setOrganization(orgResult.organization);

              // Fetch visitors for this organization
              const visitorsResponse = await fetch(`/api/dashboard/visitors-by-org/${orgResult.organization.id}`, {
                credentials: 'include'
              });
              if (visitorsResponse.ok) {
                const visitorsResult = await visitorsResponse.json();
                if (visitorsResult.success) {
                  const fetchedVisitors = visitorsResult.data;
                  setVisitors(fetchedVisitors);

                  // Calculate stats from visitors data
                  const totalTerdaftar = fetchedVisitors.length;
                  const totalDisetujui = fetchedVisitors.filter((v: VisitorData) => v.status === 'approved').length;
                  const totalDitolak = fetchedVisitors.filter((v: VisitorData) => v.status === 'rejected').length;
                  const totalKadaluwarsa = 5; // Dummy data for now

                  setStats({
                    totalPengunjungTerdaftar: totalTerdaftar,
                    totalPengunjungDisetujui: totalDisetujui,
                    totalPengunjungDitolak: totalDitolak,
                    totalPengunjungKadaluwarsa: totalKadaluwarsa,
                  });

                  // Generate chart data based on visitor registration dates (last 7 days)
                  // Start from Monday (1) to Sunday (0) for proper week order
                  const today = new Date();
                  const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.

                  // Calculate days back to Monday
                  const daysBackToMonday = currentDay === 0 ? 6 : currentDay - 1;

                  const last7Days = Array.from({ length: 7 }, (_, i) => {
                    const date = new Date();
                    date.setDate(date.getDate() - daysBackToMonday + i);
                    return date;
                  });

                  const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];

                  const chartData = last7Days.map(date => {
                    const dayName = dayNames[date.getDay()];
                    const visitorsOnDay = fetchedVisitors.filter((visitor: VisitorData) => {
                      const visitorDate = new Date(visitor.createdAt);
                      return visitorDate.toDateString() === date.toDateString();
                    }).length;

                    return {
                      day: dayName,
                      value: visitorsOnDay
                    };
                  });

                  setChartData(chartData);

                  // Generate weekly data based on visitor registration dates (last 4 weeks)
                  const last4Weeks = Array.from({ length: 4 }, (_, i) => {
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() - ((3 - i) * 7 + 6)); // Start of week
                    const endDate = new Date();
                    endDate.setDate(endDate.getDate() - ((3 - i) * 7)); // End of week
                    return { startDate, endDate, weekNumber: i + 1 };
                  });

                  const weeklyData = last4Weeks.map(({ startDate, endDate, weekNumber }) => {
                    const visitorsInWeek = fetchedVisitors.filter((visitor: VisitorData) => {
                      const visitorDate = new Date(visitor.createdAt);
                      return visitorDate >= startDate && visitorDate <= endDate;
                    }).length;

                    return {
                      day: `Minggu ${weekNumber}`,
                      value: visitorsInWeek
                    };
                  });

                  setWeeklyChartData(weeklyData);
                }
              }
            }
          }
        }

        // Stats and chart data are now calculated from visitor data above

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set fallback data
        setStats({
          totalPengunjungTerdaftar: 17,
          totalPengunjungDisetujui: 10,
          totalPengunjungDitolak: 2,
          totalPengunjungKadaluwarsa: 5,
        });
        setChartData([
          { day: 'Senin', value: 12 },
          { day: 'Selasa', value: 19 },
          { day: 'Rabu', value: 15 },
          { day: 'Kamis', value: 25 },
          { day: 'Jumat', value: 22 },
          { day: 'Sabtu', value: 18 },
          { day: 'Minggu', value: 8 },
        ]);

        setWeeklyChartData([
          { day: 'Minggu 1', value: 0 },
          { day: 'Minggu 2', value: 0 },
          { day: 'Minggu 3', value: 0 },
          { day: 'Minggu 4', value: 0 },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [slug]);

  const handleApproveVisitor = async (visitorId: string) => {
    try {
      const response = await fetch(`/api/dashboard/approve-visitor/${visitorId}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Refresh visitors data and recalculate stats
        if (organization) {
          const visitorsResponse = await fetch(`/api/dashboard/visitors-by-org/${organization.id}`, {
            credentials: 'include'
          });
          if (visitorsResponse.ok) {
            const visitorsResult = await visitorsResponse.json();
            if (visitorsResult.success) {
              const fetchedVisitors = visitorsResult.data;
              setVisitors(fetchedVisitors);

              // Recalculate stats
              const totalTerdaftar = fetchedVisitors.length;
              const totalDisetujui = fetchedVisitors.filter((v: VisitorData) => v.status === 'approved').length;
              const totalDitolak = fetchedVisitors.filter((v: VisitorData) => v.status === 'rejected').length;
              const totalKadaluwarsa = 5; // Dummy data for now

              setStats({
                totalPengunjungTerdaftar: totalTerdaftar,
                totalPengunjungDisetujui: totalDisetujui,
                totalPengunjungDitolak: totalDitolak,
                totalPengunjungKadaluwarsa: totalKadaluwarsa,
              });

              // Recalculate weekly data
              const last4Weeks = Array.from({ length: 4 }, (_, i) => {
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - ((3 - i) * 7 + 6));
                const endDate = new Date();
                endDate.setDate(endDate.getDate() - ((3 - i) * 7));
                return { startDate, endDate, weekNumber: i + 1 };
              });

              const weeklyData = last4Weeks.map(({ startDate, endDate, weekNumber }) => {
                const visitorsInWeek = fetchedVisitors.filter((visitor: VisitorData) => {
                  const visitorDate = new Date(visitor.createdAt);
                  return visitorDate >= startDate && visitorDate <= endDate;
                }).length;

                return {
                  day: `Minggu ${weekNumber}`,
                  value: visitorsInWeek
                };
              });

              setWeeklyChartData(weeklyData);
            }
          }
        }
      } else {
        const error = await response.json();
        alert(`Error approving visitor: ${error.error}`);
      }
    } catch (error) {
      console.error('Error approving visitor:', error);
      alert('Failed to approve visitor');
    }
  };

  const handleRejectVisitor = async (visitorId: string) => {
    try {
      const response = await fetch(`/api/dashboard/reject-visitor/${visitorId}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Refresh visitors data and recalculate stats
        if (organization) {
          const visitorsResponse = await fetch(`/api/dashboard/visitors-by-org/${organization.id}`, {
            credentials: 'include'
          });
          if (visitorsResponse.ok) {
            const visitorsResult = await visitorsResponse.json();
            if (visitorsResult.success) {
              const fetchedVisitors = visitorsResult.data;
              setVisitors(fetchedVisitors);

              // Recalculate stats
              const totalTerdaftar = fetchedVisitors.length;
              const totalDisetujui = fetchedVisitors.filter((v: VisitorData) => v.status === 'approved').length;
              const totalDitolak = fetchedVisitors.filter((v: VisitorData) => v.status === 'rejected').length;
              const totalKadaluwarsa = 5; // Dummy data for now

              setStats({
                totalPengunjungTerdaftar: totalTerdaftar,
                totalPengunjungDisetujui: totalDisetujui,
                totalPengunjungDitolak: totalDitolak,
                totalPengunjungKadaluwarsa: totalKadaluwarsa,
              });

              // Recalculate weekly data
              const last4Weeks = Array.from({ length: 4 }, (_, i) => {
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - ((3 - i) * 7 + 6));
                const endDate = new Date();
                endDate.setDate(endDate.getDate() - ((3 - i) * 7));
                return { startDate, endDate, weekNumber: i + 1 };
              });

              const weeklyData = last4Weeks.map(({ startDate, endDate, weekNumber }) => {
                const visitorsInWeek = fetchedVisitors.filter((visitor: VisitorData) => {
                  const visitorDate = new Date(visitor.createdAt);
                  return visitorDate >= startDate && visitorDate <= endDate;
                }).length;

                return {
                  day: `Minggu ${weekNumber}`,
                  value: visitorsInWeek
                };
              });

              setWeeklyChartData(weeklyData);
            }
          }
        }
      } else {
        const error = await response.json();
        alert(`Error rejecting visitor: ${error.error}`);
      }
    } catch (error) {
      console.error('Error rejecting visitor:', error);
      alert('Failed to reject visitor');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">
          Dashboard {organization ? `- ${organization.name}` : ''}
        </h1>
      </div>

      {/* Stats Cards */}
      <StatsCards stats={stats} />

      {/* Chart and Heatmap */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        <VisitorChart data={chartData} weeklyData={weeklyChartData} />
        <HeatmapCard />
      </div>

      {/* Enhanced Recent Visitors Table with Approval Actions */}
      <EnhancedRecentVisitors
        visitors={visitors}
        onApprove={handleApproveVisitor}
        onReject={handleRejectVisitor}
      />
    </div>
  );
}
